//
//  ParkingDetailView.swift
//  XYZ Parking
//
//  Created by <PERSON><PERSON><PERSON> on 5/8/2025.
//

import SwiftUI
import MapKit

struct ParkingDetailView: View {
    let parkingLocation: ParkingLocation
    
    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 24) {
                // Street view map
                Map(initialPosition: .region(
                    MKCoordinateRegion(
                        center: CLLocationCoordinate2D(
                            latitude: parkingLocation.latitude,
                            longitude: parkingLocation.longitude
                        ),
                        latitudinalMeters: 200,
                        longitudinalMeters: 200
                    )
                )) {
                    Annotation(parkingLocation.name, coordinate: CLLocationCoordinate2D(
                        latitude: parkingLocation.latitude,
                        longitude: parkingLocation.longitude
                    )) {
                        Image(systemName: "car.fill")
                            .foregroundColor(.white)
                            .font(.title2)
                            .padding(8)
                            .background(Color.red)
                            .clipShape(Circle())
                            .shadow(radius: 3)
                    }
                }
                .mapStyle(.satelliteFlyover)
                .disabled(true) // Disable interaction like the original
                .frame(height: 200)
                .clipShape(RoundedRectangle(cornerRadius: 16))
                .padding(.horizontal, 20)
                
                // Details section
                VStack(alignment: .leading, spacing: 20) {
                    // Title and address
                    VStack(alignment: .leading, spacing: 8) {
                        Text(parkingLocation.name)
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(.primary)

                        HStack(spacing: 6) {
                            Image(systemName: "location.fill")
                                .font(.system(size: 14))
                                .foregroundColor(.blue)
                            Text(parkingLocation.address)
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                        }
                    }

                    // Price and availability info
                    HStack(spacing: 24) {
                        VStack(alignment: .leading, spacing: 4) {
                            Text("Price")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            HStack(spacing: 2) {
                                Text(parkingLocation.price)
                                    .font(.title3)
                                    .fontWeight(.bold)
                                    .foregroundColor(.blue)
                                Text("/hr")
                                    .font(.caption)
                                    .foregroundColor(.gray)
                            }
                        }

                        VStack(alignment: .leading, spacing: 4) {
                            Text("Available Spots")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            HStack(spacing: 2) {
                                Text(parkingLocation.spots)
                                    .font(.title3)
                                    .fontWeight(.bold)
                                    .foregroundColor(.green)
                                Text("spots")
                                    .font(.caption)
                                    .foregroundColor(.gray)
                            }
                        }
                    }

                    // Action buttons
                    Button(action: {
                        // Navigate action
                        if let url = URL(string: "maps://?daddr=\(parkingLocation.latitude),\(parkingLocation.longitude)") {
                            if UIApplication.shared.canOpenURL(url) {
                                UIApplication.shared.open(url)
                            }
                        }
                    }) {
                        HStack {
                            Image(systemName: "location.north.fill")
                                .font(.system(size: 16, weight: .medium))
                            Text("Navigate")
                                .font(.system(size: 16, weight: .semibold))
                        }
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .frame(height: 50)
                        .background(Color.blue)
                        .clipShape(RoundedRectangle(cornerRadius: 12))
                    }
                }
                .padding(20)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(.thinMaterial)
                        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
                )
                .padding(.horizontal, 20)
            }
            .padding(.bottom, 20)
        }
        .background(.ultraThinMaterial) // 使用与ParkingListView一致的半透明材质背景
        .navigationTitle("Parking Details")
        .navigationBarTitleDisplayMode(.inline)
    }
}



// MARK: - Preview
struct ParkingDetailView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationStack {
            ParkingDetailView(
                parkingLocation: ParkingLocation(
                    id: 1,
                    name: "Central Parking",
                    address: "123 Collins Street, Melbourne VIC 3000",
                    price: "$15",
                    spots: "25",
                    latitude: -37.8136,
                    longitude: 144.9631
                )
            )
        }
    }
}
